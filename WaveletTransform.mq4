//+------------------------------------------------------------------+
//|                                            WaveletTransform.mq4 |
//|                        Copyright 2024, Wavelet Analysis Systems |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Wavelet Analysis Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window
#property indicator_buffers 4
#property indicator_plots   4

//--- plot Approximation
#property indicator_label1  "Approximation"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- plot Detail
#property indicator_label2  "Detail"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

//--- plot Trend Signal
#property indicator_label3  "Trend Signal"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrGreen
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

//--- plot Cycle Signal
#property indicator_label4  "Cycle Signal"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrOrange
#property indicator_style4  STYLE_SOLID
#property indicator_width4  1

//--- input parameters
input int      WaveletLevels = 3;           // عدد مستويات التحليل
input int      DataPeriod = 100;            // فترة البيانات للتحليل
input bool     ShowApproximation = true;    // عرض التقريب
input bool     ShowDetail = true;           // عرض التفاصيل
input bool     ShowTrendSignal = true;      // عرض إشارة الاتجاه
input bool     ShowCycleSignal = true;      // عرض إشارة الدورة
input double   TrendThreshold = 0.5;        // عتبة إشارة الاتجاه
input double   CycleThreshold = 0.3;        // عتبة إشارة الدورة
input bool     ShowAlerts = true;           // عرض التنبيهات
input bool     ShowComments = true;         // عرض التعليقات على الشارت
input color    TrendUpColor = clrLime;      // لون الاتجاه الصاعد
input color    TrendDownColor = clrRed;     // لون الاتجاه الهابط
input int      AlertFrequency = 60;         // تكرار التنبيهات بالثواني

//--- indicator buffers
double ApproximationBuffer[];
double DetailBuffer[];
double TrendSignalBuffer[];
double CycleSignalBuffer[];

//--- Daubechies D4 wavelet coefficients
double h0 = 0.4829629131445341;
double h1 = 0.8365163037378079;
double h2 = 0.2241438680420134;
double h3 = -0.1294095225512604;

double g0 = -0.1294095225512604;
double g1 = -0.2241438680420134;
double g2 = 0.8365163037378079;
double g3 = -0.4829629131445341;

//--- Global arrays for wavelet analysis
double prices[];
double approximation[];
double detail[];

//--- Global variables for alerts and analysis
datetime lastAlertTime = 0;
int lastSignal = 0;
double lastSupport = 0;
double lastResistance = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- indicator buffers mapping
    SetIndexBuffer(0, ApproximationBuffer);
    SetIndexBuffer(1, DetailBuffer);
    SetIndexBuffer(2, TrendSignalBuffer);
    SetIndexBuffer(3, CycleSignalBuffer);
    
    //--- set index style
    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 1, clrRed);
    SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 2, clrGreen);
    SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
    
    //--- set index labels
    SetIndexLabel(0, "Approximation");
    SetIndexLabel(1, "Detail");
    SetIndexLabel(2, "Trend Signal");
    SetIndexLabel(3, "Cycle Signal");
    
    //--- set indicator name
    IndicatorShortName("Wavelet Transform Analysis");
    
    //--- set precision
    IndicatorDigits(Digits);
    
    //--- initialize arrays
    ArrayResize(prices, DataPeriod);
    ArrayResize(approximation, DataPeriod);
    ArrayResize(detail, DataPeriod);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- check for minimum bars
    if(rates_total < DataPeriod)
        return(0);
    
    int start = MathMax(prev_calculated - 1, DataPeriod);
    
    //--- main calculation loop
    for(int i = start; i < rates_total; i++)
    {
        //--- collect price data
        for(int j = 0; j < DataPeriod; j++)
        {
            if(i - j >= 0)
                prices[DataPeriod - 1 - j] = close[i - j];
            else
                prices[DataPeriod - 1 - j] = close[0];
        }
        
        //--- perform wavelet transform
        PerformWaveletTransform();
        
        //--- calculate signals
        double trendSignal = CalculateTrendSignal();
        double cycleSignal = CalculateCycleSignal();
        int tradingSignal = GenerateTradingSignal();

        //--- calculate support and resistance levels
        double support, resistance;
        CalculateWaveletLevels(support, resistance);

        //--- fill buffers
        if(ShowApproximation)
            ApproximationBuffer[i] = approximation[DataPeriod/2];
        else
            ApproximationBuffer[i] = EMPTY_VALUE;

        if(ShowDetail)
            DetailBuffer[i] = detail[DataPeriod/2];
        else
            DetailBuffer[i] = EMPTY_VALUE;

        if(ShowTrendSignal)
            TrendSignalBuffer[i] = trendSignal;
        else
            TrendSignalBuffer[i] = EMPTY_VALUE;

        if(ShowCycleSignal)
            CycleSignalBuffer[i] = cycleSignal;
        else
            CycleSignalBuffer[i] = EMPTY_VALUE;

        //--- handle alerts and comments for the latest bar
        if(i == rates_total - 1)
        {
            HandleAlertsAndComments(tradingSignal, support, resistance);
        }
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Perform Discrete Wavelet Transform                              |
//+------------------------------------------------------------------+
void PerformWaveletTransform()
{
    int n = DataPeriod;
    double temp_approx[];
    double temp_detail[];
    
    ArrayResize(temp_approx, n);
    ArrayResize(temp_detail, n);
    
    //--- copy original data
    ArrayCopy(approximation, prices);
    
    //--- perform multi-level decomposition
    for(int level = 0; level < WaveletLevels; level++)
    {
        int half_n = n / 2;
        
        //--- forward transform
        for(int i = 0; i < half_n; i++)
        {
            int idx = i * 2;
            
            //--- approximation coefficients (low-pass)
            temp_approx[i] = h0 * approximation[idx] + 
                            h1 * approximation[(idx + 1) % n] + 
                            h2 * approximation[(idx + 2) % n] + 
                            h3 * approximation[(idx + 3) % n];
            
            //--- detail coefficients (high-pass)
            temp_detail[i] = g0 * approximation[idx] + 
                            g1 * approximation[(idx + 1) % n] + 
                            g2 * approximation[(idx + 2) % n] + 
                            g3 * approximation[(idx + 3) % n];
        }
        
        //--- copy results back
        for(int i = 0; i < half_n; i++)
        {
            approximation[i] = temp_approx[i];
            detail[i] = temp_detail[i];
        }
        
        n = half_n;
        if(n < 4) break; // minimum size for D4 wavelet
    }
}

//+------------------------------------------------------------------+
//| Calculate Trend Signal                                          |
//+------------------------------------------------------------------+
double CalculateTrendSignal()
{
    double signal = 0.0;
    int count = MathMin(DataPeriod/4, ArraySize(approximation));
    
    //--- analyze approximation coefficients for trend
    for(int i = 0; i < count; i++)
    {
        signal += approximation[i];
    }
    
    signal = signal / count;
    
    //--- normalize and apply threshold
    if(MathAbs(signal) > TrendThreshold)
        return signal > 0 ? 1.0 : -1.0;
    else
        return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate Cycle Signal                                          |
//+------------------------------------------------------------------+
double CalculateCycleSignal()
{
    double signal = 0.0;
    int count = MathMin(DataPeriod/4, ArraySize(detail));
    
    //--- analyze detail coefficients for cycles
    for(int i = 0; i < count; i++)
    {
        signal += detail[i] * detail[i]; // energy in detail coefficients
    }
    
    signal = MathSqrt(signal / count);
    
    //--- normalize and apply threshold
    if(signal > CycleThreshold)
        return signal;
    else
        return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate Wavelet Energy at Different Scales                   |
//+------------------------------------------------------------------+
double CalculateWaveletEnergy(int scale)
{
    double energy = 0.0;
    int start_idx = 0;
    int end_idx = DataPeriod / MathPow(2, scale + 1);

    for(int i = start_idx; i < end_idx && i < ArraySize(detail); i++)
    {
        energy += detail[i] * detail[i];
    }

    return MathSqrt(energy / (end_idx - start_idx));
}

//+------------------------------------------------------------------+
//| Detect Market Regime Based on Wavelet Analysis                 |
//+------------------------------------------------------------------+
string DetectMarketRegime()
{
    double low_freq_energy = CalculateWaveletEnergy(0);   // اتجاه طويل المدى
    double mid_freq_energy = CalculateWaveletEnergy(1);   // اتجاه متوسط المدى
    double high_freq_energy = CalculateWaveletEnergy(2);  // تذبذبات قصيرة المدى

    if(low_freq_energy > mid_freq_energy && low_freq_energy > high_freq_energy)
        return "Trending";
    else if(high_freq_energy > low_freq_energy && high_freq_energy > mid_freq_energy)
        return "Ranging";
    else
        return "Transitional";
}

//+------------------------------------------------------------------+
//| Calculate Wavelet-based Support and Resistance                 |
//+------------------------------------------------------------------+
void CalculateWaveletLevels(double &support, double &resistance)
{
    double current_approx = approximation[0];
    double detail_variance = 0.0;
    int count = MathMin(10, ArraySize(detail));

    //--- calculate detail variance for volatility estimation
    for(int i = 0; i < count; i++)
    {
        detail_variance += detail[i] * detail[i];
    }
    detail_variance = MathSqrt(detail_variance / count);

    //--- calculate dynamic levels
    support = current_approx - (2.0 * detail_variance);
    resistance = current_approx + (2.0 * detail_variance);
}

//+------------------------------------------------------------------+
//| Generate Trading Signals Based on Wavelet Analysis            |
//+------------------------------------------------------------------+
int GenerateTradingSignal()
{
    double trend = CalculateTrendSignal();
    double cycle = CalculateCycleSignal();
    string regime = DetectMarketRegime();

    //--- strong buy signal
    if(trend > 0 && cycle < CycleThreshold && regime == "Trending")
        return 2;  // Strong Buy

    //--- weak buy signal
    if(trend > 0 && cycle >= CycleThreshold)
        return 1;  // Weak Buy

    //--- strong sell signal
    if(trend < 0 && cycle < CycleThreshold && regime == "Trending")
        return -2; // Strong Sell

    //--- weak sell signal
    if(trend < 0 && cycle >= CycleThreshold)
        return -1; // Weak Sell

    //--- no signal
    return 0;
}

//+------------------------------------------------------------------+
//| Calculate Wavelet-based Volatility                             |
//+------------------------------------------------------------------+
double CalculateWaveletVolatility()
{
    double volatility = 0.0;
    int count = MathMin(20, ArraySize(detail));

    for(int i = 0; i < count; i++)
    {
        volatility += MathAbs(detail[i]);
    }

    return volatility / count;
}

//+------------------------------------------------------------------+
//| Inverse Wavelet Transform for Reconstruction                   |
//+------------------------------------------------------------------+
void InverseWaveletTransform(double &reconstructed[])
{
    ArrayResize(reconstructed, DataPeriod);
    ArrayCopy(reconstructed, approximation);

    int n = DataPeriod / MathPow(2, WaveletLevels);

    //--- reconstruction from wavelet coefficients
    for(int level = WaveletLevels - 1; level >= 0; level--)
    {
        n = n * 2;
        double temp[];
        ArrayResize(temp, n);

        //--- inverse transform
        for(int i = 0; i < n/2; i++)
        {
            int idx = i * 2;

            //--- reconstruction formula
            temp[idx] = h0 * reconstructed[i] + g0 * detail[i];
            temp[idx + 1] = h1 * reconstructed[i] + g1 * detail[i];

            if(idx + 2 < n)
                temp[idx + 2] += h2 * reconstructed[i] + g2 * detail[i];
            if(idx + 3 < n)
                temp[idx + 3] += h3 * reconstructed[i] + g3 * detail[i];
        }

        ArrayCopy(reconstructed, temp);
    }
}

//+------------------------------------------------------------------+
//| Handle Alerts and Comments                                      |
//+------------------------------------------------------------------+
void HandleAlertsAndComments(int signal, double support, double resistance)
{
    string regime = DetectMarketRegime();
    double volatility = CalculateWaveletVolatility();

    //--- update global variables
    lastSupport = support;
    lastResistance = resistance;

    //--- display comments on chart
    if(ShowComments)
    {
        string comment = StringFormat(
            "=== Wavelet Transform Analysis ===\n" +
            "Market Regime: %s\n" +
            "Trend Signal: %.3f\n" +
            "Cycle Signal: %.3f\n" +
            "Volatility: %.5f\n" +
            "Support Level: %.5f\n" +
            "Resistance Level: %.5f\n" +
            "Trading Signal: %s",
            regime,
            CalculateTrendSignal(),
            CalculateCycleSignal(),
            volatility,
            support,
            resistance,
            GetSignalText(signal)
        );
        Comment(comment);
    }

    //--- handle alerts
    if(ShowAlerts && signal != 0 && signal != lastSignal)
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastAlertTime >= AlertFrequency)
        {
            string alertMessage = StringFormat(
                "Wavelet Signal: %s | Regime: %s | Price: %.5f",
                GetSignalText(signal),
                regime,
                Close[0]
            );

            Alert(alertMessage);
            lastAlertTime = currentTime;
            lastSignal = signal;
        }
    }
}

//+------------------------------------------------------------------+
//| Get Signal Text Description                                     |
//+------------------------------------------------------------------+
string GetSignalText(int signal)
{
    switch(signal)
    {
        case 2:  return "Strong Buy (شراء قوي)";
        case 1:  return "Weak Buy (شراء ضعيف)";
        case 0:  return "No Signal (لا توجد إشارة)";
        case -1: return "Weak Sell (بيع ضعيف)";
        case -2: return "Strong Sell (بيع قوي)";
        default: return "Unknown (غير معروف)";
    }
}

//+------------------------------------------------------------------+
//| Calculate Fractal Dimension using Wavelet                      |
//+------------------------------------------------------------------+
double CalculateFractalDimension()
{
    double sum_log_energy = 0.0;
    double sum_log_scale = 0.0;
    int valid_levels = 0;

    for(int level = 0; level < WaveletLevels; level++)
    {
        double energy = CalculateWaveletEnergy(level);
        if(energy > 0)
        {
            sum_log_energy += MathLog(energy);
            sum_log_scale += MathLog(MathPow(2, level + 1));
            valid_levels++;
        }
    }

    if(valid_levels < 2) return 1.5; // default fractal dimension

    double slope = sum_log_energy / sum_log_scale;
    return 2.0 + slope; // Hurst exponent to fractal dimension
}

//+------------------------------------------------------------------+
//| OnDeinit function                                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Comment(""); // clear comments
}

//+------------------------------------------------------------------+
//| Additional utility functions for advanced analysis             |
//+------------------------------------------------------------------+

//--- Calculate phase information from wavelet coefficients
double CalculateWaveletPhase()
{
    double real_part = 0.0;
    double imag_part = 0.0;
    int count = MathMin(10, ArraySize(detail));

    for(int i = 0; i < count; i++)
    {
        real_part += detail[i] * MathCos(2 * M_PI * i / count);
        imag_part += detail[i] * MathSin(2 * M_PI * i / count);
    }

    return MathArctan2(imag_part, real_part);
}

//--- Detect dominant frequency in the signal
double DetectDominantFrequency()
{
    double max_energy = 0.0;
    int dominant_scale = 0;

    for(int scale = 0; scale < WaveletLevels; scale++)
    {
        double energy = CalculateWaveletEnergy(scale);
        if(energy > max_energy)
        {
            max_energy = energy;
            dominant_scale = scale;
        }
    }

    // Convert scale to approximate frequency
    return 1.0 / MathPow(2, dominant_scale + 1);
}
