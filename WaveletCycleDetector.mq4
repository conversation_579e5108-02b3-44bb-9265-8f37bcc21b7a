//+------------------------------------------------------------------+
//|                                        WaveletSquareOf9.mq4     |
//|                          مؤشر تحليل الموجات مع Square of 9      |
//|                                        Developed for MT4        |
//+------------------------------------------------------------------+
#property copyright "Wavelet + Square of 9 Indicator"
#property link      ""
#property version   "1.00"
#property strict

// إعدادات المؤشر
#property indicator_chart_window
#property indicator_buffers 6
#property indicator_plots   6

// ألوان المؤشر
#property indicator_color1  clrBlue      // الاتجاه الرئيسي
#property indicator_color2  clrRed       // مستوى البيع Square of 9
#property indicator_color3  clrGreen     // مستوى الشراء Square of 9
#property indicator_color4  clrYellow    // إشارة شراء
#property indicator_color5  clrMagenta   // إشارة بيع
#property indicator_color6  clrGray      // خط الوسط

// أنماط الخطوط
#property indicator_style1  STYLE_SOLID
#property indicator_style2  STYLE_DASH
#property indicator_style3  STYLE_DASH
#property indicator_style4  STYLE_SOLID
#property indicator_style5  STYLE_SOLID
#property indicator_style6  STYLE_DOT

// عرض الخطوط
#property indicator_width1  2
#property indicator_width2  1
#property indicator_width3  1
#property indicator_width4  3
#property indicator_width5  3
#property indicator_width6  1

//+------------------------------------------------------------------+
//| المدخلات الخارجية                                               |
//+------------------------------------------------------------------+
extern int WaveletPeriod = 24;           // فترة تحليل الموجة
extern int WaveletLevels = 3;            // عدد مستويات التحليل
extern bool WaitForCandleClose = true;   // انتظار إغلاق الشمعة
extern double SquareOf9Factor = 0.25;    // معامل Square of 9
extern bool ShowSquareLevels = true;     // إظهار مستويات Square of 9
extern bool ShowSignals = true;          // إظهار إشارات التداول
extern double SignalThreshold = 0.5;     // عتبة الإشارة
extern bool EnableAlerts = false;        // تفعيل التنبيهات
extern string AlertSound = "alert.wav";  // ملف الصوت للتنبيه

//+------------------------------------------------------------------+
//| المتغيرات العامة                                                |
//+------------------------------------------------------------------+
// مصفوفات البيانات
double WaveletTrendBuffer[];     // الاتجاه من تحليل الموجة
double SquareSellBuffer[];       // مستوى البيع Square of 9
double SquareBuyBuffer[];        // مستوى الشراء Square of 9
double BuySignalBuffer[];        // إشارات الشراء
double SellSignalBuffer[];       // إشارات البيع
double MiddleLineBuffer[];       // خط الوسط

double WorkingArray[];
double TempArray[];
datetime LastSignalTime = 0;
double LastClosePrice = 0;
bool NewCandleFormed = false;

struct SquareOf9Data {
    double referencePrice;
    double buyLevel;
    double sellLevel;
    bool isActive;
    datetime signalTime;
    bool buyLevelUsed;
    bool sellLevelUsed;
};
SquareOf9Data g_SquareData;

#ifndef EMPTY_VALUE
#define EMPTY_VALUE 0x7FFFFFFF
#endif

// دعم _Point إذا لم يكن معرفًا
#ifndef _Point
#define _Point Point
#endif

//+------------------------------------------------------------------+
//| دالة التهيئة                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // ربط المصفوفات
    SetIndexBuffer(0, WaveletTrendBuffer);
    SetIndexBuffer(1, SquareSellBuffer);
    SetIndexBuffer(2, SquareBuyBuffer);
    SetIndexBuffer(3, BuySignalBuffer);
    SetIndexBuffer(4, SellSignalBuffer);
    SetIndexBuffer(5, MiddleLineBuffer);

    // تأكد من أن جميع البفرات تعمل كسلاسل زمنية (مطلوب في مؤشرات MT4)
    ArraySetAsSeries(WaveletTrendBuffer, true);
    ArraySetAsSeries(SquareSellBuffer, true);
    ArraySetAsSeries(SquareBuyBuffer, true);
    ArraySetAsSeries(BuySignalBuffer, true);
    ArraySetAsSeries(SellSignalBuffer, true);
    ArraySetAsSeries(MiddleLineBuffer, true);
    ArraySetAsSeries(WorkingArray, true);
    ArraySetAsSeries(TempArray, true);

    SetIndexLabel(0, "اتجاه الموجة");
    SetIndexLabel(1, "مستوى البيع S9");
    SetIndexLabel(2, "مستوى الشراء S9");
    SetIndexLabel(3, "إشارة شراء");
    SetIndexLabel(4, "إشارة بيع");
    SetIndexLabel(5, "خط الوسط");

    SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrBlue);
    SetIndexStyle(1, DRAW_LINE, STYLE_DASH, 1, clrRed);
    SetIndexStyle(2, DRAW_LINE, STYLE_DASH, 1, clrGreen);
    SetIndexStyle(3, DRAW_ARROW, STYLE_SOLID, 3, clrYellow);
    SetIndexStyle(4, DRAW_ARROW, STYLE_SOLID, 3, clrMagenta);
    SetIndexStyle(5, DRAW_LINE, STYLE_DOT, 1, clrGray);

    SetIndexArrow(3, 233);
    SetIndexArrow(4, 234);

    if(!ShowSquareLevels) {
        SetIndexStyle(1, DRAW_NONE);
        SetIndexStyle(2, DRAW_NONE);
    }
    if(!ShowSignals) {
        SetIndexStyle(3, DRAW_NONE);
        SetIndexStyle(4, DRAW_NONE);
    }
    IndicatorShortName("Wavelet+Square9 (" + DoubleToStr(WaveletPeriod, 0) + ")");
    ArrayResize(WorkingArray, WaveletPeriod);
    ArrayResize(TempArray, WaveletPeriod);
    InitializeSquareOf9Data();
    Print("تم تهيئة مؤشر Wavelet + Square of 9 بنجاح");
    return INIT_SUCCEEDED;
}

void InitializeSquareOf9Data()
{
    g_SquareData.referencePrice = 0;
    g_SquareData.buyLevel = 0;
    g_SquareData.sellLevel = 0;
    g_SquareData.isActive = false;
    g_SquareData.signalTime = 0;
    g_SquareData.buyLevelUsed = false;
    g_SquareData.sellLevelUsed = false;
}

void OnDeinit(const int reason)
{
    Print("تم إلغاء تهيئة مؤشر Wavelet + Square of 9");
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < WaveletPeriod) return 0;
    // Resize buffers if needed
    ArrayResize(WaveletTrendBuffer, rates_total);
    ArrayResize(SquareSellBuffer, rates_total);
    ArrayResize(SquareBuyBuffer, rates_total);
    ArrayResize(BuySignalBuffer, rates_total);
    ArrayResize(SellSignalBuffer, rates_total);
    // تحديد نقطة البداية
    int start = MathMax(prev_calculated - 1, WaveletPeriod - 1);
    if(start < WaveletPeriod - 1) start = WaveletPeriod - 1;
    // الحساب للشموع المغلقة
    for(int i = start; i >= 1; i--) {
        CalculateWaveletSquareAnalysis(i, close, high, low);
    }
    return rates_total;
}

void CheckNewCandleFormation(const double &close[])
{
    double currentClose = close[0];
    NewCandleFormed = (LastClosePrice != 0 && LastClosePrice != currentClose);
    LastClosePrice = currentClose;
}

void CalculateWaveletSquareAnalysis(int pos, const double &close[], const double &high[], const double &low[])
{
    // تجاهل الشمعة الحالية (index 0)
    if(pos == 0) return;
    CalculateWaveletTrend(pos, close);
    CalculateMiddleLine(pos, high, low);
    ProcessWaveletSignals(pos, close);
    UpdateSquareOf9Levels(pos);
    GenerateWaveletSquareSignals(pos, close);
}

void CalculateWaveletTrend(int pos, const double &close[])
{
    for(int i = 0; i < WaveletPeriod; i++) {
        if(pos - i >= 0) {
            WorkingArray[i] = close[pos - i];
        } else {
            WorkingArray[i] = close[0];
        }
    }
    ApplySimpleWaveletTransform();
    WaveletTrendBuffer[pos] = ExtractMainTrend();
}

void ApplySimpleWaveletTransform()
{
    ArrayCopy(TempArray, WorkingArray, 0, 0, WaveletPeriod);
    for(int level = 0; level < WaveletLevels; level++) {
        ApplyLowPassFilter(TempArray, WaveletPeriod);
    }
}

void ApplyLowPassFilter(double &data[], int size)
{
    double temp[];
    ArrayResize(temp, size);
    for(int i = 1; i < size - 1; i++) {
        temp[i] = (data[i-1] + 2*data[i] + data[i+1]) / 4.0;
    }
    for(int i = 1; i < size - 1; i++) {
        data[i] = temp[i];
    }
}

double ExtractMainTrend()
{
    double trend = 0;
    int count = 0;
    int start = WaveletPeriod / 4;
    int end = 3 * WaveletPeriod / 4;
    for(int i = start; i < end; i++) {
        trend += TempArray[i];
        count++;
    }
    return (count > 0) ? trend / count : 0;
}

void CalculateMiddleLine(int pos, const double &high[], const double &low[])
{
    if(pos >= 0) {
        MiddleLineBuffer[pos] = (high[pos] + low[pos]) / 2.0;
    }
}

void ProcessWaveletSignals(int pos, const double &close[])
{
    if(pos < 2) return;
    bool waveletBuySignal = (WaveletTrendBuffer[pos] > WaveletTrendBuffer[pos-1]) &&
                           (WaveletTrendBuffer[pos-1] <= WaveletTrendBuffer[pos-2]);
    bool waveletSellSignal = (WaveletTrendBuffer[pos] < WaveletTrendBuffer[pos-1]) &&
                            (WaveletTrendBuffer[pos-1] >= WaveletTrendBuffer[pos-2]);
    if((waveletBuySignal || waveletSellSignal) && ShouldCreateNewLevels(pos)) {
        CreateSquareOf9Levels(close[pos], pos);
    }
}

bool ShouldCreateNewLevels(int pos)
{
    if(!g_SquareData.isActive) return true;
    if(g_SquareData.buyLevelUsed && g_SquareData.sellLevelUsed) return true;
    datetime currentTime = iTime(Symbol(), 0, pos);
    if(currentTime - g_SquareData.signalTime > Period() * 60 * 10) return true;
    return false;
}

void CreateSquareOf9Levels(double referencePrice, int pos)
{
    if(WaitForCandleClose && pos == 0) return;
    double sqrtPrice = MathSqrt(referencePrice);
    double offset = SquareOf9Factor * sqrtPrice;
    g_SquareData.referencePrice = referencePrice;
    g_SquareData.buyLevel = referencePrice + offset;
    g_SquareData.sellLevel = referencePrice - offset;
    g_SquareData.isActive = true;
    g_SquareData.signalTime = iTime(Symbol(), 0, pos);
    g_SquareData.buyLevelUsed = false;
    g_SquareData.sellLevelUsed = false;
    int digits = MarketInfo(Symbol(), MODE_DIGITS);
    g_SquareData.buyLevel = NormalizeDouble(g_SquareData.buyLevel, digits);
    g_SquareData.sellLevel = NormalizeDouble(g_SquareData.sellLevel, digits);
    Print("Square of 9 - مستويات جديدة: شراء=", g_SquareData.buyLevel,
          " بيع=", g_SquareData.sellLevel, " مرجع=", referencePrice);
}

void UpdateSquareOf9Levels(int pos)
{
    SquareBuyBuffer[pos] = EMPTY_VALUE;
    SquareSellBuffer[pos] = EMPTY_VALUE;
    if(g_SquareData.isActive && ShowSquareLevels) {
        SquareBuyBuffer[pos] = g_SquareData.buyLevel;
        SquareSellBuffer[pos] = g_SquareData.sellLevel;
    }
}

void GenerateWaveletSquareSignals(int pos, const double &close[])
{
    BuySignalBuffer[pos] = EMPTY_VALUE;
    SellSignalBuffer[pos] = EMPTY_VALUE;
    if(!g_SquareData.isActive || !ShowSignals) return;
    double currentPrice = close[pos];
    if(!g_SquareData.buyLevelUsed &&
       currentPrice >= g_SquareData.buyLevel &&
       WaveletTrendBuffer[pos] > MiddleLineBuffer[pos] + SignalThreshold) {
        BuySignalBuffer[pos] = currentPrice;
        g_SquareData.buyLevelUsed = true;
        if(EnableAlerts) {
            SendWaveletAlert("إشارة شراء Wavelet+S9", currentPrice, g_SquareData.buyLevel);
        }
    }
    if(!g_SquareData.sellLevelUsed &&
       currentPrice <= g_SquareData.sellLevel &&
       WaveletTrendBuffer[pos] < MiddleLineBuffer[pos] - SignalThreshold) {
        SellSignalBuffer[pos] = currentPrice;
        g_SquareData.sellLevelUsed = true;
        if(EnableAlerts) {
            SendWaveletAlert("إشارة بيع Wavelet+S9", currentPrice, g_SquareData.sellLevel);
        }
    }
    if(g_SquareData.buyLevelUsed && g_SquareData.sellLevelUsed) {
        g_SquareData.isActive = false;
    }
}

void SendWaveletAlert(string message, double price, double level)
{
    datetime currentTime = TimeCurrent();
    if(currentTime - LastSignalTime < 60) return;
    string fullMessage = message + " على " + Symbol() +
                        " السعر: " + DoubleToString(price, Digits) +
                        " المستوى: " + DoubleToString(level, Digits);
    Alert(fullMessage);
    if(AlertSound != "") {
        PlaySound(AlertSound);
    }
    LastSignalTime = currentTime;
}

string GetWaveletSquareInfo(int pos)
{
    if(pos < 0 || pos >= Bars) return "";
    string info = "Wavelet + Square of 9:\n";
    info += "اتجاه الموجة: " + DoubleToString(WaveletTrendBuffer[pos], 5) + "\n";
    info += "خط الوسط: " + DoubleToString(MiddleLineBuffer[pos], Digits) + "\n";
    if(g_SquareData.isActive) {
        info += "مستوى الشراء S9: " + DoubleToString(g_SquareData.buyLevel, Digits);
        if(g_SquareData.buyLevelUsed) info += " (مستخدم)";
        info += "\n";
        info += "مستوى البيع S9: " + DoubleToString(g_SquareData.sellLevel, Digits);
        if(g_SquareData.sellLevelUsed) info += " (مستخدم)";
        info += "\n";
        info += "السعر المرجعي: " + DoubleToString(g_SquareData.referencePrice, Digits);
    } else {
        info += "لا توجد مستويات S9 نشطة";
    }
    return info;
}

bool IsHighQualityWaveletSignal(int pos)
{
    if(pos < 3) return false;
    bool strongTrend = MathAbs(WaveletTrendBuffer[pos] - MiddleLineBuffer[pos]) > SignalThreshold;
    bool trendConsistency = true;
    for(int i = 1; i <= 2; i++) {
        if(pos - i >= 0) {
            double currentDiff = WaveletTrendBuffer[pos] - MiddleLineBuffer[pos];
            double prevDiff = WaveletTrendBuffer[pos-i] - MiddleLineBuffer[pos-i];
            if((currentDiff > 0 && prevDiff < 0) || (currentDiff < 0 && prevDiff > 0)) {
                trendConsistency = false;
                break;
            }
        }
    }
    return strongTrend && trendConsistency;
}

// لا يوجد تكرار في الخصائص أو المتغيرات أو الدوال في هذا الملف
// الكود نظيف ولا يحتاج إلى إزالة تكرارات
// إذا ظهرت لك رسائل أخطاء مشابهة في ملف آخر (مثل SimpleWavelet.mq4) قم بإزالة التكرارات هناك
