# مؤشر Wavelet Transform للتحليل الفني

## نظرة عامة
مؤشر متقدم يستخدم تقنية Wavelet Transform (التحويل المويجي) لتحليل البيانات الزمنية للأسعار واستخراج المكونات الترددية والزمنية المختلفة. يساعد هذا المؤشر في تحديد الاتجاهات والدورات المخفية في السوق.

## المميزات الرئيسية

### 1. تحليل Wavelet Transform
- **خوارزمية Discrete Wavelet Transform (DWT)**: تستخدم Daubechies D4 wavelets
- **تحليل متعدد المستويات**: يمكن تحليل البيانات على مستويات مختلفة
- **استخراج المكونات**: فصل المكونات عالية وقليلة التردد

### 2. إشارات التداول
- **إشارة الاتجاه**: تحديد الاتجاه العام للسوق
- **إشارة الدورة**: تحديد الدورات والتذبذبات
- **قوة الإشارة**: تصنيف الإشارات إلى قوية وضعيفة
- **نظام التنبيهات**: تنبيهات صوتية عند ظهور إشارات جديدة

### 3. تحليل نظام السوق
- **تحديد نظام السوق**: Trending, Ranging, أو Transitional
- **حساب التقلبات**: قياس التقلبات باستخدام Wavelet
- **مستويات الدعم والمقاومة**: حساب ديناميكي للمستويات

### 4. تحليل متقدم
- **البعد الكسري**: حساب Fractal Dimension للسوق
- **الطور المويجي**: تحليل الطور للإشارات
- **التردد المهيمن**: تحديد التردد الأكثر تأثيراً

## المعاملات القابلة للتخصيص

### معاملات التحليل
- **WaveletLevels** (3): عدد مستويات التحليل
- **DataPeriod** (100): فترة البيانات للتحليل
- **TrendThreshold** (0.5): عتبة إشارة الاتجاه
- **CycleThreshold** (0.3): عتبة إشارة الدورة

### معاملات العرض
- **ShowApproximation**: عرض مكونات التقريب
- **ShowDetail**: عرض مكونات التفاصيل
- **ShowTrendSignal**: عرض إشارة الاتجاه
- **ShowCycleSignal**: عرض إشارة الدورة
- **ShowAlerts**: تفعيل التنبيهات
- **ShowComments**: عرض التعليقات على الشارت

### معاملات التنبيهات
- **AlertFrequency** (60): تكرار التنبيهات بالثواني
- **TrendUpColor**: لون الاتجاه الصاعد
- **TrendDownColor**: لون الاتجاه الهابط

## كيفية الاستخدام

### 1. التثبيت
1. انسخ ملف `WaveletTransform.mq4` إلى مجلد `MQL4/Indicators`
2. أعد تشغيل MetaTrader 4
3. أضف المؤشر إلى الشارت من قائمة المؤشرات

### 2. التفسير
- **الخط الأزرق (Approximation)**: يمثل الاتجاه العام طويل المدى
- **الخط الأحمر (Detail)**: يمثل التذبذبات قصيرة المدى
- **الخط الأخضر (Trend Signal)**: إشارة الاتجاه (+1 صاعد، -1 هابط)
- **الخط البرتقالي (Cycle Signal)**: قوة الدورات والتذبذبات

### 3. إشارات التداول
- **Strong Buy (شراء قوي)**: اتجاه صاعد + دورات منخفضة + نظام trending
- **Weak Buy (شراء ضعيف)**: اتجاه صاعد + دورات عالية
- **Strong Sell (بيع قوي)**: اتجاه هابط + دورات منخفضة + نظام trending
- **Weak Sell (بيع ضعيف)**: اتجاه هابط + دورات عالية

## الأساس النظري

### Wavelet Transform
التحويل المويجي هو تقنية رياضية تسمح بتحليل الإشارات في كل من المجال الزمني والترددي معاً، مما يجعلها مثالية لتحليل البيانات المالية غير المستقرة.

### Daubechies Wavelets
تستخدم wavelets من عائلة Daubechies D4 التي تتميز بخصائص ممتازة لتحليل الإشارات المالية:
- دعم مضغوط
- انتظام جيد
- خصائص تمرير منخفض وعالي مناسبة

### تطبيقات السوق
- **تحديد الاتجاهات**: المكونات منخفضة التردد تكشف الاتجاهات طويلة المدى
- **كشف الدورات**: المكونات عالية التردد تكشف الدورات قصيرة المدى
- **تحليل التقلبات**: طاقة المكونات تقيس التقلبات
- **نقاط الانعكاس**: تغيرات في المكونات تشير لنقاط الانعكاس

## نصائح للاستخدام

1. **اختيار المعاملات**: ابدأ بالمعاملات الافتراضية وعدّل حسب الحاجة
2. **فترات زمنية مختلفة**: جرب المؤشر على فترات زمنية مختلفة
3. **دمج مع مؤشرات أخرى**: استخدم مع مؤشرات أخرى للتأكيد
4. **إدارة المخاطر**: استخدم دائماً إدارة مخاطر مناسبة

## ملاحظات مهمة
- المؤشر يحتاج لفترة إحماء (DataPeriod) قبل إعطاء إشارات دقيقة
- الإشارات أكثر دقة في الأسواق ذات التقلبات المعتدلة
- يُنصح بالاختبار على البيانات التاريخية قبل الاستخدام الفعلي

## الدعم الفني
للاستفسارات والدعم الفني، يرجى مراجعة التوثيق أو الاتصال بفريق التطوير.
