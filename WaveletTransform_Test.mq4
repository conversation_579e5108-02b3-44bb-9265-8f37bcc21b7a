//+------------------------------------------------------------------+
//|                                      WaveletTransform_Test.mq4 |
//|                        Copyright 2024, Wavelet Analysis Systems |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Wavelet Analysis Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property script_show_inputs

//--- input parameters for testing
input int TestBars = 1000;              // عدد الشموع للاختبار
input bool RunPerformanceTest = true;   // تشغيل اختبار الأداء
input bool RunAccuracyTest = true;      // تشغيل اختبار الدقة
input bool ShowDetailedResults = true;  // عرض النتائج التفصيلية

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== بدء اختبار مؤشر Wavelet Transform ===");
    
    if(RunPerformanceTest)
    {
        Print("--- اختبار الأداء ---");
        TestPerformance();
    }
    
    if(RunAccuracyTest)
    {
        Print("--- اختبار الدقة ---");
        TestAccuracy();
    }
    
    Print("=== انتهاء الاختبار ===");
}

//+------------------------------------------------------------------+
//| Test Performance of Wavelet Transform                           |
//+------------------------------------------------------------------+
void TestPerformance()
{
    datetime startTime = GetTickCount();
    
    //--- simulate wavelet transform calculations
    double testData[];
    ArrayResize(testData, TestBars);
    
    //--- generate test data (sine wave + noise)
    for(int i = 0; i < TestBars; i++)
    {
        testData[i] = MathSin(2 * M_PI * i / 50) + 
                      0.5 * MathSin(2 * M_PI * i / 20) + 
                      0.1 * (MathRand() / 32767.0 - 0.5);
    }
    
    //--- perform multiple wavelet transforms
    int iterations = 100;
    for(int iter = 0; iter < iterations; iter++)
    {
        SimulateWaveletTransform(testData);
    }
    
    datetime endTime = GetTickCount();
    double executionTime = (endTime - startTime) / 1000.0;
    
    Print(StringFormat("وقت التنفيذ: %.3f ثانية لـ %d تكرار", executionTime, iterations));
    Print(StringFormat("متوسط الوقت لكل تكرار: %.3f مللي ثانية", executionTime * 1000 / iterations));
}

//+------------------------------------------------------------------+
//| Test Accuracy of Wavelet Transform                              |
//+------------------------------------------------------------------+
void TestAccuracy()
{
    //--- test with known signal
    double knownSignal[];
    double reconstructed[];
    ArrayResize(knownSignal, 64);
    ArrayResize(reconstructed, 64);
    
    //--- create test signal (combination of frequencies)
    for(int i = 0; i < 64; i++)
    {
        knownSignal[i] = MathSin(2 * M_PI * i / 16) +     // low frequency
                         0.5 * MathSin(2 * M_PI * i / 4);  // high frequency
    }
    
    //--- perform forward and inverse transform
    double approximation[], detail[];
    ArrayResize(approximation, 64);
    ArrayResize(detail, 64);
    
    SimulateWaveletDecomposition(knownSignal, approximation, detail);
    SimulateWaveletReconstruction(approximation, detail, reconstructed);
    
    //--- calculate reconstruction error
    double totalError = 0.0;
    for(int i = 0; i < 64; i++)
    {
        double error = MathAbs(knownSignal[i] - reconstructed[i]);
        totalError += error;
    }
    
    double averageError = totalError / 64;
    Print(StringFormat("متوسط خطأ إعادة البناء: %.6f", averageError));
    
    if(averageError < 0.001)
        Print("✓ اختبار الدقة: نجح");
    else
        Print("✗ اختبار الدقة: فشل");
}

//+------------------------------------------------------------------+
//| Simulate Wavelet Transform for Performance Testing              |
//+------------------------------------------------------------------+
void SimulateWaveletTransform(double &data[])
{
    int n = ArraySize(data);
    double temp[];
    ArrayResize(temp, n);
    
    //--- Daubechies D4 coefficients
    double h0 = 0.4829629131445341;
    double h1 = 0.8365163037378079;
    double h2 = 0.2241438680420134;
    double h3 = -0.1294095225512604;
    
    //--- forward transform (one level)
    for(int i = 0; i < n/2; i++)
    {
        int idx = i * 2;
        temp[i] = h0 * data[idx] + 
                  h1 * data[(idx + 1) % n] + 
                  h2 * data[(idx + 2) % n] + 
                  h3 * data[(idx + 3) % n];
    }
}

//+------------------------------------------------------------------+
//| Simulate Wavelet Decomposition                                  |
//+------------------------------------------------------------------+
void SimulateWaveletDecomposition(double &signal[], double &approx[], double &detail[])
{
    int n = ArraySize(signal);
    
    //--- Daubechies D4 coefficients
    double h0 = 0.4829629131445341;
    double h1 = 0.8365163037378079;
    double h2 = 0.2241438680420134;
    double h3 = -0.1294095225512604;
    
    double g0 = -0.1294095225512604;
    double g1 = -0.2241438680420134;
    double g2 = 0.8365163037378079;
    double g3 = -0.4829629131445341;
    
    //--- decomposition
    for(int i = 0; i < n/2; i++)
    {
        int idx = i * 2;
        
        //--- approximation coefficients
        approx[i] = h0 * signal[idx] + 
                    h1 * signal[(idx + 1) % n] + 
                    h2 * signal[(idx + 2) % n] + 
                    h3 * signal[(idx + 3) % n];
        
        //--- detail coefficients
        detail[i] = g0 * signal[idx] + 
                    g1 * signal[(idx + 1) % n] + 
                    g2 * signal[(idx + 2) % n] + 
                    g3 * signal[(idx + 3) % n];
    }
}

//+------------------------------------------------------------------+
//| Simulate Wavelet Reconstruction                                 |
//+------------------------------------------------------------------+
void SimulateWaveletReconstruction(double &approx[], double &detail[], double &reconstructed[])
{
    int n = ArraySize(reconstructed);
    
    //--- Daubechies D4 coefficients for reconstruction
    double h0 = 0.4829629131445341;
    double h1 = 0.8365163037378079;
    double h2 = 0.2241438680420134;
    double h3 = -0.1294095225512604;
    
    double g0 = -0.1294095225512604;
    double g1 = -0.2241438680420134;
    double g2 = 0.8365163037378079;
    double g3 = -0.4829629131445341;
    
    //--- initialize reconstructed array
    ArrayInitialize(reconstructed, 0.0);
    
    //--- reconstruction
    for(int i = 0; i < n/2; i++)
    {
        int idx = i * 2;
        
        //--- add approximation contribution
        reconstructed[idx] += h0 * approx[i];
        reconstructed[(idx + 1) % n] += h1 * approx[i];
        reconstructed[(idx + 2) % n] += h2 * approx[i];
        reconstructed[(idx + 3) % n] += h3 * approx[i];
        
        //--- add detail contribution
        reconstructed[idx] += g0 * detail[i];
        reconstructed[(idx + 1) % n] += g1 * detail[i];
        reconstructed[(idx + 2) % n] += g2 * detail[i];
        reconstructed[(idx + 3) % n] += g3 * detail[i];
    }
}

//+------------------------------------------------------------------+
//| Test Signal Generation Functions                                |
//+------------------------------------------------------------------+
void TestSignalGeneration()
{
    Print("--- اختبار توليد الإشارات ---");
    
    //--- test different market conditions
    TestTrendingMarket();
    TestRangingMarket();
    TestVolatileMarket();
}

//+------------------------------------------------------------------+
//| Test Trending Market Conditions                                |
//+------------------------------------------------------------------+
void TestTrendingMarket()
{
    double trendData[];
    ArrayResize(trendData, 100);
    
    //--- generate trending data
    for(int i = 0; i < 100; i++)
    {
        trendData[i] = i * 0.01 + 0.05 * MathSin(2 * M_PI * i / 20);
    }
    
    //--- analyze with wavelet
    double approx[], detail[];
    ArrayResize(approx, 100);
    ArrayResize(detail, 100);
    
    SimulateWaveletDecomposition(trendData, approx, detail);
    
    //--- calculate trend strength
    double trendStrength = CalculateTrendStrength(approx);
    Print(StringFormat("قوة الاتجاه في السوق الاتجاهي: %.3f", trendStrength));
}

//+------------------------------------------------------------------+
//| Test Ranging Market Conditions                                 |
//+------------------------------------------------------------------+
void TestRangingMarket()
{
    double rangeData[];
    ArrayResize(rangeData, 100);
    
    //--- generate ranging data
    for(int i = 0; i < 100; i++)
    {
        rangeData[i] = MathSin(2 * M_PI * i / 10) + 0.3 * MathSin(2 * M_PI * i / 5);
    }
    
    //--- analyze with wavelet
    double approx[], detail[];
    ArrayResize(approx, 100);
    ArrayResize(detail, 100);
    
    SimulateWaveletDecomposition(rangeData, approx, detail);
    
    //--- calculate cycle strength
    double cycleStrength = CalculateCycleStrength(detail);
    Print(StringFormat("قوة الدورة في السوق المحدود: %.3f", cycleStrength));
}

//+------------------------------------------------------------------+
//| Test Volatile Market Conditions                                |
//+------------------------------------------------------------------+
void TestVolatileMarket()
{
    double volatileData[];
    ArrayResize(volatileData, 100);
    
    //--- generate volatile data
    for(int i = 0; i < 100; i++)
    {
        volatileData[i] = (MathRand() / 32767.0 - 0.5) * 2.0;
    }
    
    //--- analyze with wavelet
    double approx[], detail[];
    ArrayResize(approx, 100);
    ArrayResize(detail, 100);
    
    SimulateWaveletDecomposition(volatileData, approx, detail);
    
    //--- calculate volatility
    double volatility = CalculateVolatility(detail);
    Print(StringFormat("التقلبات في السوق المتقلب: %.3f", volatility));
}

//+------------------------------------------------------------------+
//| Calculate Trend Strength                                       |
//+------------------------------------------------------------------+
double CalculateTrendStrength(double &approx[])
{
    double sum = 0.0;
    int count = ArraySize(approx) / 2;
    
    for(int i = 0; i < count; i++)
    {
        sum += MathAbs(approx[i]);
    }
    
    return sum / count;
}

//+------------------------------------------------------------------+
//| Calculate Cycle Strength                                       |
//+------------------------------------------------------------------+
double CalculateCycleStrength(double &detail[])
{
    double sum = 0.0;
    int count = ArraySize(detail) / 2;
    
    for(int i = 0; i < count; i++)
    {
        sum += detail[i] * detail[i];
    }
    
    return MathSqrt(sum / count);
}

//+------------------------------------------------------------------+
//| Calculate Volatility                                           |
//+------------------------------------------------------------------+
double CalculateVolatility(double &detail[])
{
    double sum = 0.0;
    int count = ArraySize(detail) / 2;
    
    for(int i = 0; i < count; i++)
    {
        sum += MathAbs(detail[i]);
    }
    
    return sum / count;
}
